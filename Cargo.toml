[workspace]
members = ["libmpv-sys"]

[package]
name = "libmpv2"
version = "5.0.1"
edition = "2024"
authors = ["kohsine <<EMAIL>>"]
license = "LGPL-2.1"
build = "build.rs"
readme = "README.md"
description = "Libmpv abstraction that's easy to use and can play next to all codecs and containers"
repository = "https://github.com/kohsine/libmpv-rs"
keywords = ["media", "playback", "mpv", "libmpv"]


[dependencies]
glutin = "0.32.3"
libmpv2-sys = { path = "libmpv-sys", version = "4.0.0" }

[dev-dependencies]
crossbeam = "0.8.4"
sdl2 = "0.38"
serde_json = "1.0.140"

[features]
default = ["render"]
render = []          # Enable custom rendering
build_libmpv = []    # build libmpv automatically, provided MPV_SOURCE is set
