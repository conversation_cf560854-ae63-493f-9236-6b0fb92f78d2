use glutin::{
    config::ConfigTemplateBuilder,
    context::{Context<PERSON>pi, ContextAttributesBuilder, Version},
    display::{Display, DisplayApiPreference},
    prelude::*,
    surface::{Surface, SurfaceAttributesBuilder, WindowSurface},
};
use libmpv2::{
    Mpv,
    render::{OpenGLInitParams, RenderContext, RenderParam, RenderParamApiType},
};
use std::{
    env,
    ffi::{CStr, c_void},
    num::NonZeroU32,
};
use winit::{
    application::ApplicationHandler,
    event::WindowEvent,
    event_loop::{ActiveEventLoop, ControlFlow, EventLoop},
    raw_window_handle::{HasDisplayHandle, HasWindowHandle},
    window::{Window, WindowId},
};

/// Example demonstrating video rendering using glutin for OpenGL context creation
/// and window management, integrated with libmpv for video playback.
///
/// This example shows how to:
/// - Create a window and OpenGL context using glutin
/// - Initialize libmpv with custom OpenGL rendering
/// - Handle window events and render video frames
/// - Properly manage OpenGL resources and cleanup

const VIDEO_URL: &str = "test-data/jellyfish.mp4";

/// Application state holding all the necessary components for video rendering
struct App {
    window: Option<Window>,
    gl_display: Option<Display>,
    gl_context: Option<glutin::context::PossiblyCurrentContext>,
    gl_surface: Option<Surface<WindowSurface>>,
    mpv: Option<Mpv>,
    render_context: Option<RenderContext>,
}

impl App {
    fn new() -> Self {
        Self {
            window: None,
            gl_display: None,
            gl_context: None,
            gl_surface: None,
            mpv: None,
            render_context: None,
        }
    }

    fn init_gl(&mut self, event_loop: &ActiveEventLoop) -> Result<(), Box<dyn std::error::Error>> {
        let window_attributes = Window::default_attributes()
            .with_title("Glutin MPV Render")
            .with_inner_size(winit::dpi::LogicalSize::new(960, 540));

        let window = event_loop.create_window(window_attributes)?;

        // Create OpenGL display
        let gl_display =
            unsafe { Display::new(window.display_handle()?.into(), DisplayApiPreference::Egl)? };

        // Create OpenGL config
        let config_template = ConfigTemplateBuilder::new()
            .with_alpha_size(8)
            .with_transparency(false);

        let config = unsafe { gl_display.find_configs(config_template.build())? }
            .reduce(|accum, config| {
                let transparency_check = config.supports_transparency().unwrap_or(false)
                    & !accum.supports_transparency().unwrap_or(false);

                if transparency_check || config.num_samples() < accum.num_samples() {
                    config
                } else {
                    accum
                }
            })
            .ok_or("No suitable GL config found")?;

        // Create OpenGL context
        let context_attributes = ContextAttributesBuilder::new()
            .with_context_api(ContextApi::OpenGl(Some(Version::new(3, 3))))
            .build(Some(window.window_handle()?.as_raw()));

        let gl_context = unsafe { gl_display.create_context(&config, &context_attributes)? };

        // Create surface
        let surface_attributes = SurfaceAttributesBuilder::<WindowSurface>::new().build(
            window.window_handle()?.as_raw(),
            NonZeroU32::new(960).unwrap(),
            NonZeroU32::new(540).unwrap(),
        );

        let gl_surface = unsafe { gl_display.create_window_surface(&config, &surface_attributes)? };

        // Make context current
        let gl_context = gl_context.make_current(&gl_surface)?;

        self.window = Some(window);
        self.gl_display = Some(gl_display);
        self.gl_context = Some(gl_context);
        self.gl_surface = Some(gl_surface);

        Ok(())
    }

    fn init_mpv(&mut self) -> Result<(), Box<dyn std::error::Error>> {
        let path = env::args()
            .nth(1)
            .unwrap_or_else(|| String::from(VIDEO_URL));

        let mut mpv = Mpv::with_initializer(|init| {
            init.set_property("vo", "libmpv")?;
            Ok(())
        })?;

        let gl_display = self.gl_display.as_ref().unwrap();
        let render_context = RenderContext::new(
            unsafe { mpv.ctx.as_mut() },
            vec![
                RenderParam::ApiType(RenderParamApiType::OpenGl),
                RenderParam::InitParams(OpenGLInitParams {
                    get_proc_address: get_proc_address,
                    ctx: gl_display.clone(),
                }),
            ],
        )?;

        mpv.disable_deprecated_events()?;
        mpv.command("loadfile", &[&path, "replace"])?;

        self.mpv = Some(mpv);
        self.render_context = Some(render_context);

        Ok(())
    }

    fn render(&mut self) -> Result<(), Box<dyn std::error::Error>> {
        if let (Some(render_context), Some(gl_context), Some(gl_surface), Some(window)) = (
            &self.render_context,
            &self.gl_context,
            &self.gl_surface,
            &self.window,
        ) {
            let size = window.inner_size();
            render_context.render::<Display>(0, size.width as i32, size.height as i32, true)?;
            gl_surface.swap_buffers(gl_context)?;
        }
        Ok(())
    }
}

impl ApplicationHandler for App {
    fn resumed(&mut self, event_loop: &ActiveEventLoop) {
        if self.window.is_none() {
            if let Err(e) = self.init_gl(event_loop) {
                eprintln!("Failed to initialize OpenGL: {}", e);
                event_loop.exit();
                return;
            }

            if let Err(e) = self.init_mpv() {
                eprintln!("Failed to initialize MPV: {}", e);
                event_loop.exit();
                return;
            }
        }
    }

    fn window_event(
        &mut self,
        event_loop: &ActiveEventLoop,
        _window_id: WindowId,
        event: WindowEvent,
    ) {
        match event {
            WindowEvent::CloseRequested => {
                event_loop.exit();
            }
            WindowEvent::RedrawRequested => {
                if let Err(e) = self.render() {
                    eprintln!("Render error: {}", e);
                }
            }
            WindowEvent::Resized(size) => {
                if let (Some(gl_context), Some(gl_surface)) = (&self.gl_context, &self.gl_surface) {
                    gl_surface.resize(
                        gl_context,
                        NonZeroU32::new(size.width.max(1)).unwrap(),
                        NonZeroU32::new(size.height.max(1)).unwrap(),
                    );
                }
            }
            _ => {}
        }
    }

    fn about_to_wait(&mut self, _event_loop: &ActiveEventLoop) {
        if let Some(window) = &self.window {
            window.request_redraw();
        }
    }
}

/// OpenGL function pointer retrieval callback for libmpv
/// This function is called by libmpv to get OpenGL function pointers
fn get_proc_address(display: &Display, name: &str) -> *mut c_void {
    let name_with_null = format!("{}\0", name);
    let c_name = CStr::from_bytes_with_nul(name_with_null.as_bytes()).unwrap();
    display.get_proc_address(c_name) as *mut c_void
}

fn main() -> Result<(), Box<dyn std::error::Error>> {
    let event_loop = EventLoop::new()?;
    event_loop.set_control_flow(ControlFlow::Poll);

    let mut app = App::new();
    event_loop.run_app(&mut app)?;

    Ok(())
}
