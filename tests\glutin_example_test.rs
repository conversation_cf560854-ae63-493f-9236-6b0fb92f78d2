/// Test to verify that the glutin_render example compiles and has the correct structure
#[test]
fn test_glutin_example_compiles() {
    // This test ensures that the glutin_render example compiles successfully
    // The actual compilation is tested by the build system, but this test
    // can be extended to verify specific functionality if needed.
    
    // For now, we just verify that the test data file exists
    assert!(std::path::Path::new("test-data/jellyfish.mp4").exists(), 
            "Test video file should exist for the example to work properly");
}

#[test]
fn test_example_dependencies() {
    // Verify that the required dependencies are available
    // This is a compile-time check that the necessary crates are linked
    
    // These should compile if the dependencies are correctly configured
    let _: Option<winit::event_loop::EventLoop<()>> = None;
    let _: Option<glutin::display::Display> = None;
    let _: Option<libmpv2::Mpv> = None;
}
